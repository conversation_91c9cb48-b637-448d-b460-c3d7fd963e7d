import { globalShortcut, app } from "electron"
import { IShortcutsHelperDeps } from "./main"
import { configHelper } from "./ConfigHelper"

export class ShortcutsHelper
{
  private deps: IShortcutsHelperDeps

  constructor( deps: IShortcutsHelperDeps )
  {
    this.deps = deps
  }

  private adjustOpacity ( delta: number ): void
  {
    const mainWindow = this.deps.getMainWindow();
    if ( !mainWindow ) return;

    let currentOpacity = mainWindow.getOpacity();
    let newOpacity = Math.max( 0.1, Math.min( 1.0, currentOpacity + delta ) );
    console.log( `Adjusting opacity from ${ currentOpacity } to ${ newOpacity }` );

    mainWindow.setOpacity( newOpacity );

    // Save the opacity setting to config without re-initializing the client
    try
    {
      const config = configHelper.loadConfig();
      config.opacity = newOpacity;
      configHelper.saveConfig( config );
    } catch ( error )
    {
      console.error( 'Error saving opacity to config:', error );
    }

    // If we're making the window visible, also make sure it's shown and interaction is enabled
    if ( newOpacity > 0.1 && !this.deps.isVisible() )
    {
      this.deps.toggleMainWindow();
    }
  }

  public registerGlobalShortcuts (): void
  {
    console.log( "🔧 Registering ALL keyboard shortcuts for Interview Coder..." );

    // Clear any existing shortcuts
    globalShortcut.unregisterAll();

    // 🚀 CORE NAVIGATION SHORTCUTS
    globalShortcut.register( "CommandOrControl+B", () =>
    {
      console.log( "🎯 Ctrl+B: Toggle window visibility" )
      this.deps.toggleMainWindow()
    } )

    globalShortcut.register( "CommandOrControl+D", () =>
    {
      console.log( "🎯 Ctrl+D: Switch to DSA mode" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "switch-mode", "queue" )
      }
    } )

    globalShortcut.register( "CommandOrControl+C", () =>
    {
      console.log( "🎯 Ctrl+C: Switch to Chatbot mode" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "switch-mode", "chatbot" )
      }
    } )

    globalShortcut.register( "CommandOrControl+N", () =>
    {
      console.log( "🎯 Ctrl+N: Switch to Interview mode" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "switch-mode", "interview" )
      }
    } )

    globalShortcut.register( "CommandOrControl+Q", () =>
    {
      console.log( "🎯 Ctrl+Q: Quitting complete application..." )

      // Unregister all shortcuts first
      globalShortcut.unregisterAll()

      // Close main window
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow && !mainWindow.isDestroyed() )
      {
        mainWindow.close()
      }

      // Force quit the application
      app.quit()

      // Force exit if app.quit() doesn't work
      setTimeout( () =>
      {
        process.exit( 0 )
      }, 1000 )
    } )

    // 📸 DSA MODE SHORTCUTS
    globalShortcut.register( "CommandOrControl+H", async () =>
    {
      console.log( "🎯 Ctrl+H: Taking screenshot..." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        try
        {
          const screenshotPath = await this.deps.takeScreenshot()
          const preview = await this.deps.getImagePreview( screenshotPath )
          mainWindow.webContents.send( "screenshot-taken", {
            path: screenshotPath,
            preview
          } )
        } catch ( error )
        {
          console.error( "Error capturing screenshot:", error )
        }
      }
    } )

    globalShortcut.register( "CommandOrControl+Enter", async () =>
    {
      console.log( "🎯 Ctrl+Enter: Analyzing screenshots..." )
      await this.deps.processingHelper?.processScreenshots()
    } )

    globalShortcut.register( "CommandOrControl+R", async () =>
    {
      console.log( "🎯 Ctrl+R: Fast coding mode" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-processing-mode", "fast" )
      }
      await this.deps.processingHelper?.processScreenshots()
    } )

    globalShortcut.register( "CommandOrControl+Shift+S", async () =>
    {
      console.log( "🎯 Ctrl+Shift+S: MCQ mode" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-processing-mode", "mcq" )
      }
      await this.deps.processingHelper?.processScreenshots()
    } )

    // 🔤 LANGUAGE SELECTION SHORTCUTS (Alt+0-4)
    globalShortcut.register( "Alt+0", async () =>
    {
      console.log( "🎯 Alt+0: Setting language to C" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        // Save to config first
        try
        {
          const configHelper = require( './ConfigHelper' ).default;
          await configHelper.updateConfig( { language: "c" } );
        } catch ( error )
        {
          console.error( "Failed to save language to config:", error );
        }

        mainWindow.webContents.send( "set-language", "c" )
      }
    } )

    globalShortcut.register( "Alt+1", async () =>
    {
      console.log( "🎯 Alt+1: Setting language to C++" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        // Save to config first
        try
        {
          const configHelper = require( './ConfigHelper' ).default;
          await configHelper.updateConfig( { language: "cpp" } );
        } catch ( error )
        {
          console.error( "Failed to save language to config:", error );
        }

        mainWindow.webContents.send( "set-language", "cpp" )
      }
    } )

    globalShortcut.register( "Alt+2", async () =>
    {
      console.log( "🎯 Alt+2: Setting language to Java" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        // Save to config first
        try
        {
          const configHelper = require( './ConfigHelper' ).default;
          await configHelper.updateConfig( { language: "java" } );
        } catch ( error )
        {
          console.error( "Failed to save language to config:", error );
        }

        mainWindow.webContents.send( "set-language", "java" )
      }
    } )

    globalShortcut.register( "Alt+3", async () =>
    {
      console.log( "🎯 Alt+3: Setting language to SQL" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        // Save to config first
        try
        {
          const configHelper = require( './ConfigHelper' ).default;
          await configHelper.updateConfig( { language: "sql" } );
        } catch ( error )
        {
          console.error( "Failed to save language to config:", error );
        }

        mainWindow.webContents.send( "set-language", "sql" )
      }
    } )

    globalShortcut.register( "Alt+4", async () =>
    {
      console.log( "🎯 Alt+4: Setting language to JavaScript" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        // Save to config first
        try
        {
          const configHelper = require( './ConfigHelper' ).default;
          await configHelper.updateConfig( { language: "javascript" } );
        } catch ( error )
        {
          console.error( "Failed to save language to config:", error );
        }

        mainWindow.webContents.send( "set-language", "javascript" )
      }
    } )

    // 🪟 WINDOW MANAGEMENT SHORTCUTS
    globalShortcut.register( "CommandOrControl+[", () =>
    {
      console.log( "🎯 Ctrl+[: Increase opacity" )
      this.increaseOpacity()
    } )

    globalShortcut.register( "CommandOrControl+]", () =>
    {
      console.log( "🎯 Ctrl+]: Decrease opacity" )
      this.decreaseOpacity()
    } )

    globalShortcut.register( "CommandOrControl+M", () =>
    {
      console.log( "🎯 Ctrl+M: Progressive window sizing" )
      this.progressiveWindowSizing()
    } )

    globalShortcut.register( "CommandOrControl+Shift+M", () =>
    {
      console.log( "🎯 Ctrl+Shift+M: Reset window size" )
      this.resetWindowSize()
    } )

    globalShortcut.register( "CommandOrControl+Left", () =>
    {
      console.log( "🎯 Ctrl+Left: Move window left" )
      this.deps.moveWindowLeft()
    } )

    globalShortcut.register( "CommandOrControl+Right", () =>
    {
      console.log( "🎯 Ctrl+Right: Move window right" )
      this.deps.moveWindowRight()
    } )

    globalShortcut.register( "CommandOrControl+Up", () =>
    {
      console.log( "🎯 Ctrl+Up: Move window up" )
      this.deps.moveWindowUp()
    } )

    globalShortcut.register( "CommandOrControl+Down", () =>
    {
      console.log( "🎯 Ctrl+Down: Move window down" )
      this.deps.moveWindowDown()
    } )

    // 📋 CODE COPYING SHORTCUTS
    globalShortcut.register( "CommandOrControl+Shift+C", () =>
    {
      console.log( "🎯 Ctrl+Shift+C: Copy last code block" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "copy-code-block", "last" )
      }
    } )

    globalShortcut.register( "CommandOrControl+Shift+1", () =>
    {
      console.log( "🎯 Ctrl+Shift+1: Copy 1st code block" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "copy-code-block", 1 )
      }
    } )

    globalShortcut.register( "CommandOrControl+Shift+2", () =>
    {
      console.log( "🎯 Ctrl+Shift+2: Copy 2nd code block" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "copy-code-block", 2 )
      }
    } )

    globalShortcut.register( "CommandOrControl+Shift+3", () =>
    {
      console.log( "🎯 Ctrl+Shift+3: Copy 3rd code block" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "copy-code-block", 3 )
      }
    } )

    // ⚙️ SETTINGS & API SHORTCUTS
    globalShortcut.register( "CommandOrControl+S", () =>
    {
      console.log( "🎯 Ctrl+S: Open settings" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "show-settings-dialog" )
      }
    } )

    globalShortcut.register( "CommandOrControl+W", () =>
    {
      console.log( "🎯 Ctrl+W: Switch to Qwen API" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "switch-api", "qwen" )
      }
    } )

    globalShortcut.register( "CommandOrControl+G", () =>
    {
      console.log( "🎯 Ctrl+G: Switch to Gemini API" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "switch-api", "gemini" )
      }
    } )

    globalShortcut.register( "CommandOrControl+O", () =>
    {
      console.log( "🎯 Ctrl+O: Switch to OpenAI API" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "switch-api", "openai" )
      }
    } )

    globalShortcut.register( "CommandOrControl+Q", () =>
    {
      console.log( "Command/Ctrl + Q pressed. Quitting application." )
      app.quit()
    } )

    // Removed duplicate opacity shortcuts - they are registered above

    // Zoom controls
    globalShortcut.register( "CommandOrControl+-", () =>
    {
      console.log( "Command/Ctrl + - pressed. Zooming out." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        const currentZoom = mainWindow.webContents.getZoomLevel()
        mainWindow.webContents.setZoomLevel( currentZoom - 0.5 )
      }
    } )

    globalShortcut.register( "CommandOrControl+0", () =>
    {
      console.log( "Command/Ctrl + 0 pressed. Resetting zoom." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.setZoomLevel( 0 )
      }
    } )

    globalShortcut.register( "CommandOrControl+=", () =>
    {
      console.log( "Command/Ctrl + = pressed. Zooming in." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        const currentZoom = mainWindow.webContents.getZoomLevel()
        mainWindow.webContents.setZoomLevel( currentZoom + 0.5 )
      }
    } )

    // Delete last screenshot shortcut
    globalShortcut.register( "CommandOrControl+L", () =>
    {
      console.log( "Command/Ctrl + L pressed. Deleting last screenshot." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        // Send an event to the renderer to delete the last screenshot
        mainWindow.webContents.send( "delete-last-screenshot" )
      }
    } )

    // Language selection shortcuts (1/2/3/4)
    globalShortcut.register( "1", () =>
    {
      console.log( "1 pressed. Setting language to C++" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-language", "cpp" )
      }
    } )

    globalShortcut.register( "2", () =>
    {
      console.log( "2 pressed. Setting language to Java" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-language", "java" )
      }
    } )

    globalShortcut.register( "3", () =>
    {
      console.log( "3 pressed. Setting language to Python" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-language", "python" )
      }
    } )

    globalShortcut.register( "4", () =>
    {
      console.log( "4 pressed. Setting language to JavaScript" )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-language", "javascript" )
      }
    } )

    // Fast mode shortcut (Ctrl+R is already used for reset, let's use Ctrl+F)
    globalShortcut.register( "CommandOrControl+F", async () =>
    {
      console.log( "Command/Ctrl + F pressed. Fast mode processing." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-processing-mode", "fast" )
      }
      await this.deps.processingHelper?.processScreenshots()
    } )

    // MCQ mode shortcut
    globalShortcut.register( "CommandOrControl+Shift+S", async () =>
    {
      console.log( "Command/Ctrl + Shift + S pressed. MCQ mode processing." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "set-processing-mode", "mcq" )
      }
      await this.deps.processingHelper?.processScreenshots()
    } )

    // Settings shortcut
    globalShortcut.register( "CommandOrControl+S", () =>
    {
      console.log( "Command/Ctrl + S pressed. Opening settings." )
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        mainWindow.webContents.send( "show-settings-dialog" )
      }
    } )

    // Progressive window sizing shortcut
    globalShortcut.register( "CommandOrControl+M", () =>
    {
      console.log( "Command/Ctrl + M pressed. Progressive window sizing." )
      this.progressiveWindowSizing()
    } )

    // Reset window size shortcut
    globalShortcut.register( "CommandOrControl+Shift+M", () =>
    {
      console.log( "Command/Ctrl + Shift + M pressed. Resetting window size." )
      this.resetWindowSize()
    } )

    // Removed duplicate opacity shortcuts - they are registered above

    // Unregister shortcuts when quitting
    app.on( "will-quit", () =>
    {
      globalShortcut.unregisterAll()
    } )

    console.log( "✅ ALL keyboard shortcuts registered successfully!" );
  }

  private progressiveWindowSizing (): void
  {
    const mainWindow = this.deps.getMainWindow();
    if ( !mainWindow ) return;

    const currentBounds = mainWindow.getBounds();
    const sizes = [
      { width: 800, height: 600 },   // 100%
      { width: 480, height: 320 },   // 60%
      { width: 360, height: 240 },   // 45%
      { width: 240, height: 160 },   // 30%
      { width: 150, height: 100 }    // 18.75% (Super Stealth!)
    ];

    // Find current size index or default to 0
    let currentIndex = sizes.findIndex( size =>
      Math.abs( currentBounds.width - size.width ) < 50 &&
      Math.abs( currentBounds.height - size.height ) < 50
    );

    // Move to next size, or wrap to first if at end
    const nextIndex = currentIndex >= 0 ? ( currentIndex + 1 ) % sizes.length : 1;
    const newSize = sizes[ nextIndex ];

    console.log( `Progressive sizing: ${ currentIndex } -> ${ nextIndex }, Size: ${ newSize.width }x${ newSize.height }` );

    mainWindow.setSize( newSize.width, newSize.height );

    // Center the window after resizing
    const { screen } = require( 'electron' );
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

    mainWindow.setPosition(
      Math.round( ( screenWidth - newSize.width ) / 2 ),
      Math.round( ( screenHeight - newSize.height ) / 2 )
    );
  }

  private resetWindowSize (): void
  {
    const mainWindow = this.deps.getMainWindow();
    if ( !mainWindow ) return;

    console.log( "Resetting window to original size: 800x600" );
    mainWindow.setSize( 800, 600 );

    // Center the window
    const { screen } = require( 'electron' );
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

    mainWindow.setPosition(
      Math.round( ( screenWidth - 800 ) / 2 ),
      Math.round( ( screenHeight - 600 ) / 2 )
    );
  }

  private decreaseOpacity (): void
  {
    const mainWindow = this.deps.getMainWindow();
    if ( !mainWindow ) return;

    const currentOpacity = mainWindow.getOpacity();
    const newOpacity = Math.max( 0.1, currentOpacity - 0.1 );

    mainWindow.setOpacity( newOpacity );
    console.log( `Opacity decreased to: ${ newOpacity.toFixed( 1 ) }` );

    // Update config
    const { configHelper } = require( './ConfigHelper' );
    configHelper.setOpacity( newOpacity );
  }

  private increaseOpacity (): void
  {
    const mainWindow = this.deps.getMainWindow();
    if ( !mainWindow ) return;

    const currentOpacity = mainWindow.getOpacity();
    const newOpacity = Math.min( 1.0, currentOpacity + 0.1 );

    mainWindow.setOpacity( newOpacity );
    console.log( `Opacity increased to: ${ newOpacity.toFixed( 1 ) }` );

    // Update config
    const { configHelper } = require( './ConfigHelper' );
    configHelper.setOpacity( newOpacity );
  }
}
