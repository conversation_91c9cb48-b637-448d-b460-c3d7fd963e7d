import React, { useState, useEffect } from 'react'

interface LanguageIndicatorProps {
  currentLanguage: string
}

const LANGUAGE_DISPLAY_NAMES: Record<string, string> = {
  'c': 'C',
  'cpp': 'C++',
  'java': 'Java',
  'sql': 'SQL',
  'javascript': 'JavaScript',
  'python': 'Python',
  'csharp': 'C#',
  'go': 'Go',
  'rust': 'Rust',
  'typescript': 'TypeScript'
}

export const LanguageIndicator: React.FC<LanguageIndicatorProps> = ({ currentLanguage }) => {
  const [showIndicator, setShowIndicator] = useState(false)
  const [lastLanguage, setLastLanguage] = useState(currentLanguage)

  useEffect(() => {
    if (currentLanguage !== lastLanguage) {
      setShowIndicator(true)
      setLastLanguage(currentLanguage)
      
      // Hide after 2 seconds
      const timer = setTimeout(() => {
        setShowIndicator(false)
      }, 2000)

      return () => clearTimeout(timer)
    }
  }, [currentLanguage, lastLanguage])

  if (!showIndicator) return null

  const displayName = LANGUAGE_DISPLAY_NAMES[currentLanguage] || currentLanguage.toUpperCase()

  return (
    <div className="fixed top-4 right-4 z-50 animate-in fade-in-0 slide-in-from-top-2 duration-300">
      <div className="bg-black/90 border border-white/20 rounded-lg px-4 py-2 shadow-lg backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-white/90 text-sm font-medium">
            Language: {displayName}
          </span>
        </div>
      </div>
    </div>
  )
}
