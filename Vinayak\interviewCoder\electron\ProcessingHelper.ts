// ProcessingHelper.ts
import fs from "node:fs"
import path from "node:path"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import * as axios from "axios"
import { app, BrowserWindow, dialog } from "electron"
import { OpenAI } from "openai"
import { configHelper } from "./ConfigHelper"
import Anthropic from '@anthropic-ai/sdk';

// Interface for Gemini API requests
interface GeminiMessage
{
  role: string;
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    }
  }>;
}

interface GeminiResponse
{
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
}
interface AnthropicMessage
{
  role: 'user' | 'assistant';
  content: Array<{
    type: 'text' | 'image';
    text?: string;
    source?: {
      type: 'base64';
      media_type: string;
      data: string;
    };
  }>;
}
export class ProcessingHelper
{
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper
  private openaiClient: OpenAI | null = null
  private geminiApiKey: string | null = null
  private anthropicClient: Anthropic | null = null
  private qwenApiKey: string | null = null
  private groqApiKey: string | null = null

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor( deps: IProcessingHelperDeps )
  {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()

    // Initialize AI client based on config
    this.initializeAIClient();

    // Listen for config changes to re-initialize the AI client
    configHelper.on( 'config-updated', () =>
    {
      this.initializeAIClient();
    } );
  }

  /**
   * Initialize or reinitialize the AI client with current config
   */
  private initializeAIClient (): void
  {
    try
    {
      const config = configHelper.loadConfig();

      if ( config.apiProvider === "openai" )
      {
        if ( config.apiKey )
        {
          this.openaiClient = new OpenAI( {
            apiKey: config.apiKey,
            timeout: 60000, // 60 second timeout
            maxRetries: 2   // Retry up to 2 times
          } );
          this.geminiApiKey = null;
          this.anthropicClient = null;
          console.log( "OpenAI client initialized successfully" );
        } else
        {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          console.warn( "No API key available, OpenAI client not initialized" );
        }
      } else if ( config.apiProvider === "gemini" )
      {
        // Gemini client initialization
        this.openaiClient = null;
        this.anthropicClient = null;
        if ( config.apiKey )
        {
          this.geminiApiKey = config.apiKey;
          console.log( "Gemini API key set successfully" );
        } else
        {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          console.warn( "No API key available, Gemini client not initialized" );
        }
      } else if ( config.apiProvider === "anthropic" )
      {
        // Reset other clients
        this.openaiClient = null;
        this.geminiApiKey = null;
        if ( config.apiKey )
        {
          this.anthropicClient = new Anthropic( {
            apiKey: config.apiKey,
            timeout: 60000,
            maxRetries: 2
          } );
          console.log( "Anthropic client initialized successfully" );
        } else
        {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          this.qwenApiKey = null;
          console.warn( "No API key available, Anthropic client not initialized" );
        }
      } else if ( config.apiProvider === "qwen" )
      {
        // Reset other clients
        this.openaiClient = null;
        this.geminiApiKey = null;
        this.anthropicClient = null;
        if ( config.apiKey )
        {
          this.qwenApiKey = config.apiKey;
          console.log( "Qwen API key set successfully" );
        } else
        {
          this.openaiClient = null;
          this.geminiApiKey = null;
          this.anthropicClient = null;
          this.qwenApiKey = null;
          console.warn( "No API key available, Qwen client not initialized" );
        }
      }
    } catch ( error )
    {
      console.error( "Failed to initialize AI client:", error );
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
      this.qwenApiKey = null;
      this.groqApiKey = null;
    }

    // Initialize Groq API key from environment
    try
    {
      const groqApiKey = process.env.GROQ_API_KEY;
      if ( groqApiKey )
      {
        this.groqApiKey = groqApiKey;
        console.log( "Groq API key loaded successfully" );
      }
    } catch ( error )
    {
      console.warn( "Groq API key not found, voice features will be limited" );
    }
  }

  private async waitForInitialization (
    mainWindow: BrowserWindow
  ): Promise<void>
  {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while ( attempts < maxAttempts )
    {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if ( isInitialized ) return
      await new Promise( ( resolve ) => setTimeout( resolve, 100 ) )
      attempts++
    }
    throw new Error( "App failed to initialize after 5 seconds" )
  }

  private async getCredits (): Promise<number>
  {
    const mainWindow = this.deps.getMainWindow()
    if ( !mainWindow ) return 999 // Unlimited credits in this version

    try
    {
      await this.waitForInitialization( mainWindow )
      return 999 // Always return sufficient credits to work
    } catch ( error )
    {
      console.error( "Error getting credits:", error )
      return 999 // Unlimited credits as fallback
    }
  }

  private async getLanguage (): Promise<string>
  {
    try
    {
      // Get language from config
      const config = configHelper.loadConfig();
      if ( config.language )
      {
        return config.language;
      }

      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if ( mainWindow )
      {
        try
        {
          await this.waitForInitialization( mainWindow )
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          )
          {
            return language;
          }
        } catch ( err )
        {
          console.warn( "Could not get language from window", err );
        }
      }

      // Default fallback
      return "cpp";
    } catch ( error )
    {
      console.error( "Error getting language:", error )
      return "cpp"
    }
  }

  public async processScreenshots (): Promise<void>
  {
    const mainWindow = this.deps.getMainWindow()
    if ( !mainWindow ) return

    const config = configHelper.loadConfig();

    // First verify we have a valid AI client
    if ( config.apiProvider === "openai" && !this.openaiClient )
    {
      this.initializeAIClient();

      if ( !this.openaiClient )
      {
        console.error( "OpenAI client not initialized" );
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if ( config.apiProvider === "gemini" && !this.geminiApiKey )
    {
      this.initializeAIClient();

      if ( !this.geminiApiKey )
      {
        console.error( "Gemini API key not initialized" );
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if ( config.apiProvider === "anthropic" && !this.anthropicClient )
    {
      // Add check for Anthropic client
      this.initializeAIClient();

      if ( !this.anthropicClient )
      {
        console.error( "Anthropic client not initialized" );
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if ( config.apiProvider === "qwen" && !this.qwenApiKey )
    {
      // Add check for Qwen API key
      this.initializeAIClient();

      if ( !this.qwenApiKey )
      {
        console.error( "Qwen API key not initialized" );
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    }

    const view = this.deps.getView()
    console.log( "Processing screenshots in view:", view )

    if ( view === "queue" )
    {
      mainWindow.webContents.send( this.deps.PROCESSING_EVENTS.INITIAL_START )
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log( "Processing main queue screenshots:", screenshotQueue )

      // Check if the queue is empty
      if ( !screenshotQueue || screenshotQueue.length === 0 )
      {
        console.log( "No screenshots found in queue" );
        mainWindow.webContents.send( this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS );
        return;
      }

      // Check that files actually exist
      const existingScreenshots = screenshotQueue.filter( path => fs.existsSync( path ) );
      if ( existingScreenshots.length === 0 )
      {
        console.log( "Screenshot files don't exist on disk" );
        mainWindow.webContents.send( this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS );
        return;
      }

      try
      {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          existingScreenshots.map( async ( path ) =>
          {
            try
            {
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview( path ),
                data: fs.readFileSync( path ).toString( 'base64' )
              };
            } catch ( err )
            {
              console.error( `Error reading screenshot ${ path }:`, err );
              return null;
            }
          } )
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter( Boolean );

        if ( validScreenshots.length === 0 )
        {
          throw new Error( "Failed to load screenshot data" );
        }

        const result = await this.processScreenshotsHelper( validScreenshots, signal )

        if ( !result.success )
        {
          console.log( "Processing failed:", result.error )
          if ( result.error?.includes( "API Key" ) || result.error?.includes( "OpenAI" ) || result.error?.includes( "Gemini" ) )
          {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else
          {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // Reset view back to queue on error
          console.log( "Resetting view to queue due to error" )
          this.deps.setView( "queue" )
          return
        }

        // Only set view to solutions if processing succeeded
        console.log( "Setting view to solutions after successful processing" )
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView( "solutions" )
      } catch ( error: any )
      {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error( "Processing error:", error )
        if ( axios.isCancel( error ) )
        {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else
        {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message || "Server error. Please try again."
          )
        }
        // Reset view back to queue on error
        console.log( "Resetting view to queue due to error" )
        this.deps.setView( "queue" )
      } finally
      {
        this.currentProcessingAbortController = null
      }
    } else
    {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log( "Processing extra queue screenshots:", extraScreenshotQueue )

      // Check if the extra queue is empty
      if ( !extraScreenshotQueue || extraScreenshotQueue.length === 0 )
      {
        console.log( "No extra screenshots found in queue" );
        mainWindow.webContents.send( this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS );

        return;
      }

      // Check that files actually exist
      const existingExtraScreenshots = extraScreenshotQueue.filter( path => fs.existsSync( path ) );
      if ( existingExtraScreenshots.length === 0 )
      {
        console.log( "Extra screenshot files don't exist on disk" );
        mainWindow.webContents.send( this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS );
        return;
      }

      mainWindow.webContents.send( this.deps.PROCESSING_EVENTS.DEBUG_START )

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try
      {
        // Get all screenshots (both main and extra) for processing
        const allPaths = [
          ...this.screenshotHelper.getScreenshotQueue(),
          ...existingExtraScreenshots
        ];

        const screenshots = await Promise.all(
          allPaths.map( async ( path ) =>
          {
            try
            {
              if ( !fs.existsSync( path ) )
              {
                console.warn( `Screenshot file does not exist: ${ path }` );
                return null;
              }

              return {
                path,
                preview: await this.screenshotHelper.getImagePreview( path ),
                data: fs.readFileSync( path ).toString( 'base64' )
              };
            } catch ( err )
            {
              console.error( `Error reading screenshot ${ path }:`, err );
              return null;
            }
          } )
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter( Boolean );

        if ( validScreenshots.length === 0 )
        {
          throw new Error( "Failed to load screenshot data for debugging" );
        }

        console.log(
          "Combined screenshots for processing:",
          validScreenshots.map( ( s ) => s.path )
        )

        const result = await this.processExtraScreenshotsHelper(
          validScreenshots,
          signal
        )

        if ( result.success )
        {
          this.deps.setHasDebugged( true )
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else
        {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch ( error: any )
      {
        if ( axios.isCancel( error ) )
        {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else
        {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            error.message
          )
        }
      } finally
      {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper (
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  )
  {
    try
    {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();

      // Step 1: Extract problem info using AI Vision API (OpenAI or Gemini)
      const imageDataList = screenshots.map( screenshot => screenshot.data );

      // Update the user on progress
      if ( mainWindow )
      {
        mainWindow.webContents.send( "processing-status", {
          message: "Analyzing problem from screenshots...",
          progress: 20
        } );
      }

      let problemInfo;

      if ( config.apiProvider === "openai" )
      {
        // Verify OpenAI client
        if ( !this.openaiClient )
        {
          this.initializeAIClient(); // Try to reinitialize

          if ( !this.openaiClient )
          {
            return {
              success: false,
              error: "OpenAI API key not configured or invalid. Please check your settings."
            };
          }
        }

        // Use OpenAI for processing
        const messages = [
          {
            role: "system" as const,
            content: "You are a coding challenge interpreter. Analyze the screenshot of the coding problem and extract all relevant information. Return the information in JSON format with these fields: problem_statement, constraints, example_input, example_output. Just return the structured JSON without any other text."
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `Extract the coding problem details from these screenshots. Return in JSON format. Preferred coding language we gonna use for this problem is ${ language }.`
              },
              ...imageDataList.map( data => ( {
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${ data }` }
              } ) )
            ]
          }
        ];

        // Send to OpenAI Vision API
        const extractionResponse = await this.openaiClient.chat.completions.create( {
          model: config.extractionModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        } );

        // Parse the response
        try
        {
          const responseText = extractionResponse.choices[ 0 ].message.content;
          // Handle when OpenAI might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace( /```json|```/g, '' ).trim();
          problemInfo = JSON.parse( jsonText );
        } catch ( error )
        {
          console.error( "Error parsing OpenAI response:", error );
          return {
            success: false,
            error: "Failed to parse problem information. Please try again or use clearer screenshots."
          };
        }
      } else if ( config.apiProvider === "gemini" )
      {
        // Use Gemini API
        if ( !this.geminiApiKey )
        {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try
        {
          // Create Gemini message structure
          const geminiMessages: GeminiMessage[] = [
            {
              role: "user",
              parts: [
                {
                  text: `You are a coding challenge interpreter. Analyze the screenshots of the coding problem and extract all relevant information. Return the information in JSON format with these fields: problem_statement, constraints, example_input, example_output. Just return the structured JSON without any other text. Preferred coding language we gonna use for this problem is ${ language }.`
                },
                ...imageDataList.map( data => ( {
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                } ) )
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${ config.extractionModel || "gemini-2.0-flash" }:generateContent?key=${ this.geminiApiKey }`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if ( !responseData.candidates || responseData.candidates.length === 0 )
          {
            throw new Error( "Empty response from Gemini API" );
          }

          const responseText = responseData.candidates[ 0 ].content.parts[ 0 ].text;

          // Handle when Gemini might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace( /```json|```/g, '' ).trim();
          problemInfo = JSON.parse( jsonText );
        } catch ( error )
        {
          console.error( "Error using Gemini API:", error );
          return {
            success: false,
            error: "Failed to process with Gemini API. Please check your API key or try again later."
          };
        }
      } else if ( config.apiProvider === "anthropic" )
      {
        if ( !this.anthropicClient )
        {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try
        {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `Extract the coding problem details from these screenshots. Return in JSON format with these fields: problem_statement, constraints, example_input, example_output. Preferred coding language is ${ language }.`
                },
                ...imageDataList.map( data => ( {
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                } ) )
              ]
            }
          ];

          const response = await this.anthropicClient.messages.create( {
            model: config.extractionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          } );

          const responseText = ( response.content[ 0 ] as { type: 'text', text: string } ).text;
          const jsonText = responseText.replace( /```json|```/g, '' ).trim();
          problemInfo = JSON.parse( jsonText );
        } catch ( error: any )
        {
          console.error( "Error using Anthropic API:", error );

          // Add specific handling for Claude's limitations
          if ( error.status === 429 )
          {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if ( error.status === 413 || ( error.message && error.message.includes( "token" ) ) )
          {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process with Anthropic API. Please check your API key or try again later."
          };
        }
      } else if ( config.apiProvider === "qwen" )
      {
        // Use Qwen API
        if ( !this.qwenApiKey )
        {
          return {
            success: false,
            error: "Qwen API key not configured. Please check your settings."
          };
        }

        try
        {
          // Create Qwen message structure (OpenAI-compatible)
          const messages = [
            {
              role: "system",
              content: "You are a coding challenge interpreter. Analyze the screenshot of the coding problem and extract all relevant information. Return the information in JSON format with these fields: problem_statement, constraints, example_input, example_output. Just return the structured JSON without any other text."
            },
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: `Extract the coding problem details from these screenshots. Return in JSON format. Preferred coding language we gonna use for this problem is ${ language }.`
                },
                ...imageDataList.map( data => ( {
                  type: "image_url",
                  image_url: { url: `data:image/png;base64,${ data }` }
                } ) )
              ]
            }
          ];

          // Make API request to Qwen (OpenRouter)
          const response = await axios.default.post(
            "https://openrouter.ai/api/v1/chat/completions",
            {
              model: config.extractionModel || "qwen/qwq-32b:free",
              messages: messages,
              max_tokens: 4000,
              temperature: 0.2
            },
            {
              headers: {
                "Authorization": `Bearer ${ this.qwenApiKey }`,
                "Content-Type": "application/json"
              },
              signal
            }
          );

          const responseText = response.data.choices[ 0 ].message.content;
          const jsonText = responseText.replace( /```json|```/g, '' ).trim();
          problemInfo = JSON.parse( jsonText );
        } catch ( error )
        {
          console.error( "Error using Qwen API:", error );
          return {
            success: false,
            error: "Failed to process with Qwen API. Please check your API key or try again later."
          };
        }
      }

      // Update the user on progress
      if ( mainWindow )
      {
        mainWindow.webContents.send( "processing-status", {
          message: "Problem analyzed successfully. Preparing to generate solution...",
          progress: 40
        } );
      }

      // Store problem info in AppState
      this.deps.setProblemInfo( problemInfo );

      // Send first success event
      if ( mainWindow )
      {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
          problemInfo
        );

        // Generate solutions after successful extraction
        const solutionsResult = await this.generateSolutionsHelper( signal );
        if ( solutionsResult.success )
        {
          // Clear any existing extra screenshots before transitioning to solutions view
          this.screenshotHelper.clearExtraScreenshotQueue();

          // Final progress update
          mainWindow.webContents.send( "processing-status", {
            message: "Solution generated successfully",
            progress: 100
          } );

          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          );
          return { success: true, data: solutionsResult.data };
        } else
        {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          );
        }
      }

      return { success: false, error: "Failed to process screenshots" };
    } catch ( error: any )
    {
      // If the request was cancelled, don't retry
      if ( axios.isCancel( error ) )
      {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      // Handle OpenAI API errors specifically
      if ( error?.response?.status === 401 )
      {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if ( error?.response?.status === 429 )
      {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      } else if ( error?.response?.status === 500 )
      {
        return {
          success: false,
          error: "OpenAI server error. Please try again later."
        };
      }

      console.error( "API Error Details:", error );
      return {
        success: false,
        error: error.message || "Failed to process screenshots. Please try again."
      };
    }
  }

  private async generateSolutionsHelper ( signal: AbortSignal )
  {
    try
    {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if ( !problemInfo )
      {
        throw new Error( "No problem info available" );
      }

      // Update progress status
      if ( mainWindow )
      {
        mainWindow.webContents.send( "processing-status", {
          message: "Creating optimal solution with detailed explanations...",
          progress: 60
        } );
      }

      // Create prompt for solution generation
      const promptText = `
Generate a detailed solution for the following coding problem:

PROBLEM STATEMENT:
${ problemInfo.problem_statement }

CONSTRAINTS:
${ problemInfo.constraints || "No specific constraints provided." }

EXAMPLE INPUT:
${ problemInfo.example_input || "No example input provided." }

EXAMPLE OUTPUT:
${ problemInfo.example_output || "No example output provided." }

LANGUAGE: ${ language }

I need the response in the following format:
1. Code: A clean, optimized implementation in ${ language }
2. Your Thoughts: A list of key insights and reasoning behind your approach
3. Time complexity: O(X) with a detailed explanation (at least 2 sentences)
4. Space complexity: O(X) with a detailed explanation (at least 2 sentences)

For complexity explanations, please be thorough. For example: "Time complexity: O(n) because we iterate through the array only once. This is optimal as we need to examine each element at least once to find the solution." or "Space complexity: O(n) because in the worst case, we store all elements in the hashmap. The additional space scales linearly with the input size."

Your solution should be efficient, well-commented, and handle edge cases.
`;

      let responseContent;

      if ( config.apiProvider === "openai" )
      {
        // OpenAI processing
        if ( !this.openaiClient )
        {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }

        // Send to OpenAI API
        const solutionResponse = await this.openaiClient.chat.completions.create( {
          model: config.solutionModel || "gpt-4o",
          messages: [
            { role: "system", content: "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." },
            { role: "user", content: promptText }
          ],
          max_tokens: 4000,
          temperature: 0.2
        } );

        responseContent = solutionResponse.choices[ 0 ].message.content;
      } else if ( config.apiProvider === "gemini" )
      {
        // Gemini processing
        if ( !this.geminiApiKey )
        {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try
        {
          // Create Gemini message structure
          const geminiMessages = [
            {
              role: "user",
              parts: [
                {
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${ promptText }`
                }
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${ config.solutionModel || "gemini-2.0-flash" }:generateContent?key=${ this.geminiApiKey }`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if ( !responseData.candidates || responseData.candidates.length === 0 )
          {
            throw new Error( "Empty response from Gemini API" );
          }

          responseContent = responseData.candidates[ 0 ].content.parts[ 0 ].text;
        } catch ( error )
        {
          console.error( "Error using Gemini API for solution:", error );
          return {
            success: false,
            error: "Failed to generate solution with Gemini API. Please check your API key or try again later."
          };
        }
      } else if ( config.apiProvider === "anthropic" )
      {
        // Anthropic processing
        if ( !this.anthropicClient )
        {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try
        {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${ promptText }`
                }
              ]
            }
          ];

          // Send to Anthropic API
          const response = await this.anthropicClient.messages.create( {
            model: config.solutionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          } );

          responseContent = ( response.content[ 0 ] as { type: 'text', text: string } ).text;
        } catch ( error: any )
        {
          console.error( "Error using Anthropic API for solution:", error );

          // Add specific handling for Claude's limitations
          if ( error.status === 429 )
          {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if ( error.status === 413 || ( error.message && error.message.includes( "token" ) ) )
          {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to generate solution with Anthropic API. Please check your API key or try again later."
          };
        }
      } else if ( config.apiProvider === "qwen" )
      {
        // Qwen processing
        if ( !this.qwenApiKey )
        {
          return {
            success: false,
            error: "Qwen API key not configured. Please check your settings."
          };
        }

        try
        {
          // Create Qwen message structure (OpenAI-compatible)
          const messages = [
            { role: "system", content: "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." },
            { role: "user", content: promptText }
          ];

          // Make API request to Qwen (OpenRouter)
          const response = await axios.default.post(
            "https://openrouter.ai/api/v1/chat/completions",
            {
              model: config.solutionModel || "qwen/qwq-32b:free",
              messages: messages,
              max_tokens: 4000,
              temperature: 0.2
            },
            {
              headers: {
                "Authorization": `Bearer ${ this.qwenApiKey }`,
                "Content-Type": "application/json"
              },
              signal
            }
          );

          responseContent = response.data.choices[ 0 ].message.content;
        } catch ( error )
        {
          console.error( "Error using Qwen API for solution:", error );
          return {
            success: false,
            error: "Failed to generate solution with Qwen API. Please check your API key or try again later."
          };
        }
      }

      // Extract parts from the response
      const codeMatch = responseContent.match( /```(?:\w+)?\s*([\s\S]*?)```/ );
      const code = codeMatch ? codeMatch[ 1 ].trim() : responseContent;

      // Extract thoughts, looking for bullet points or numbered lists
      const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)(?:Time complexity:|$)/i;
      const thoughtsMatch = responseContent.match( thoughtsRegex );
      let thoughts: string[] = [];

      if ( thoughtsMatch && thoughtsMatch[ 1 ] )
      {
        // Extract bullet points or numbered items
        const bulletPoints = thoughtsMatch[ 1 ].match( /(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g );
        if ( bulletPoints )
        {
          thoughts = bulletPoints.map( point =>
            point.replace( /^\s*(?:[-*•]|\d+\.)\s*/, '' ).trim()
          ).filter( Boolean );
        } else
        {
          // If no bullet points found, split by newlines and filter empty lines
          thoughts = thoughtsMatch[ 1 ].split( '\n' )
            .map( ( line ) => line.trim() )
            .filter( Boolean );
        }
      }

      // Extract complexity information
      const timeComplexityPattern = /Time complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Space complexity|$))/i;
      const spaceComplexityPattern = /Space complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:[A-Z]|$))/i;

      let timeComplexity = "O(n) - Linear time complexity because we only iterate through the array once. Each element is processed exactly one time, and the hashmap lookups are O(1) operations.";
      let spaceComplexity = "O(n) - Linear space complexity because we store elements in the hashmap. In the worst case, we might need to store all elements before finding the solution pair.";

      const timeMatch = responseContent.match( timeComplexityPattern );
      if ( timeMatch && timeMatch[ 1 ] )
      {
        timeComplexity = timeMatch[ 1 ].trim();
        if ( !timeComplexity.match( /O\([^)]+\)/i ) )
        {
          timeComplexity = `O(n) - ${ timeComplexity }`;
        } else if ( !timeComplexity.includes( '-' ) && !timeComplexity.includes( 'because' ) )
        {
          const notationMatch = timeComplexity.match( /O\([^)]+\)/i );
          if ( notationMatch )
          {
            const notation = notationMatch[ 0 ];
            const rest = timeComplexity.replace( notation, '' ).trim();
            timeComplexity = `${ notation } - ${ rest }`;
          }
        }
      }

      const spaceMatch = responseContent.match( spaceComplexityPattern );
      if ( spaceMatch && spaceMatch[ 1 ] )
      {
        spaceComplexity = spaceMatch[ 1 ].trim();
        if ( !spaceComplexity.match( /O\([^)]+\)/i ) )
        {
          spaceComplexity = `O(n) - ${ spaceComplexity }`;
        } else if ( !spaceComplexity.includes( '-' ) && !spaceComplexity.includes( 'because' ) )
        {
          const notationMatch = spaceComplexity.match( /O\([^)]+\)/i );
          if ( notationMatch )
          {
            const notation = notationMatch[ 0 ];
            const rest = spaceComplexity.replace( notation, '' ).trim();
            spaceComplexity = `${ notation } - ${ rest }`;
          }
        }
      }

      const formattedResponse = {
        code: code,
        thoughts: thoughts.length > 0 ? thoughts : [ "Solution approach based on efficiency and readability" ],
        time_complexity: timeComplexity,
        space_complexity: spaceComplexity
      };

      return { success: true, data: formattedResponse };
    } catch ( error: any )
    {
      if ( axios.isCancel( error ) )
      {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      if ( error?.response?.status === 401 )
      {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if ( error?.response?.status === 429 )
      {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      }

      console.error( "Solution generation error:", error );
      return { success: false, error: error.message || "Failed to generate solution" };
    }
  }

  private async processExtraScreenshotsHelper (
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  )
  {
    try
    {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if ( !problemInfo )
      {
        throw new Error( "No problem info available" );
      }

      // Update progress status
      if ( mainWindow )
      {
        mainWindow.webContents.send( "processing-status", {
          message: "Processing debug screenshots...",
          progress: 30
        } );
      }

      // Prepare the images for the API call
      const imageDataList = screenshots.map( screenshot => screenshot.data );

      let debugContent;

      if ( config.apiProvider === "openai" )
      {
        if ( !this.openaiClient )
        {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }

        const messages = [
          {
            role: "system" as const,
            content: `You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

Your response MUST follow this exact structure with these section headers (use ### for headers):
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).`
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `I'm solving this coding problem: "${ problemInfo.problem_statement }" in ${ language }. I need help with debugging or improving my solution. Here are screenshots of my code, the errors or test cases. Please provide a detailed analysis with:
1. What issues you found in my code
2. Specific improvements and corrections
3. Any optimizations that would make the solution better
4. A clear explanation of the changes needed`
              },
              ...imageDataList.map( data => ( {
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${ data }` }
              } ) )
            ]
          }
        ];

        if ( mainWindow )
        {
          mainWindow.webContents.send( "processing-status", {
            message: "Analyzing code and generating debug feedback...",
            progress: 60
          } );
        }

        const debugResponse = await this.openaiClient.chat.completions.create( {
          model: config.debuggingModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        } );

        debugContent = debugResponse.choices[ 0 ].message.content;
      } else if ( config.apiProvider === "gemini" )
      {
        if ( !this.geminiApiKey )
        {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try
        {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${ problemInfo.problem_statement }" in ${ language }. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).
`;

          const geminiMessages = [
            {
              role: "user",
              parts: [
                { text: debugPrompt },
                ...imageDataList.map( data => ( {
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                } ) )
              ]
            }
          ];

          if ( mainWindow )
          {
            mainWindow.webContents.send( "processing-status", {
              message: "Analyzing code and generating debug feedback with Gemini...",
              progress: 60
            } );
          }

          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${ config.debuggingModel || "gemini-2.0-flash" }:generateContent?key=${ this.geminiApiKey }`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if ( !responseData.candidates || responseData.candidates.length === 0 )
          {
            throw new Error( "Empty response from Gemini API" );
          }

          debugContent = responseData.candidates[ 0 ].content.parts[ 0 ].text;
        } catch ( error )
        {
          console.error( "Error using Gemini API for debugging:", error );
          return {
            success: false,
            error: "Failed to process debug request with Gemini API. Please check your API key or try again later."
          };
        }
      } else if ( config.apiProvider === "anthropic" )
      {
        if ( !this.anthropicClient )
        {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try
        {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${ problemInfo.problem_statement }" in ${ language }. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification.
`;

          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: debugPrompt
                },
                ...imageDataList.map( data => ( {
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                } ) )
              ]
            }
          ];

          if ( mainWindow )
          {
            mainWindow.webContents.send( "processing-status", {
              message: "Analyzing code and generating debug feedback with Claude...",
              progress: 60
            } );
          }

          const response = await this.anthropicClient.messages.create( {
            model: config.debuggingModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          } );

          debugContent = ( response.content[ 0 ] as { type: 'text', text: string } ).text;
        } catch ( error: any )
        {
          console.error( "Error using Anthropic API for debugging:", error );

          // Add specific handling for Claude's limitations
          if ( error.status === 429 )
          {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if ( error.status === 413 || ( error.message && error.message.includes( "token" ) ) )
          {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process debug request with Anthropic API. Please check your API key or try again later."
          };
        }
      }


      if ( mainWindow )
      {
        mainWindow.webContents.send( "processing-status", {
          message: "Debug analysis complete",
          progress: 100
        } );
      }

      let extractedCode = "// Debug mode - see analysis below";
      const codeMatch = debugContent.match( /```(?:[a-zA-Z]+)?([\s\S]*?)```/ );
      if ( codeMatch && codeMatch[ 1 ] )
      {
        extractedCode = codeMatch[ 1 ].trim();
      }

      let formattedDebugContent = debugContent;

      if ( !debugContent.includes( '# ' ) && !debugContent.includes( '## ' ) )
      {
        formattedDebugContent = debugContent
          .replace( /issues identified|problems found|bugs found/i, '## Issues Identified' )
          .replace( /code improvements|improvements|suggested changes/i, '## Code Improvements' )
          .replace( /optimizations|performance improvements/i, '## Optimizations' )
          .replace( /explanation|detailed analysis/i, '## Explanation' );
      }

      const bulletPoints = formattedDebugContent.match( /(?:^|\n)[ ]*(?:[-*•]|\d+\.)[ ]+([^\n]+)/g );
      const thoughts = bulletPoints
        ? bulletPoints.map( point => point.replace( /^[ ]*(?:[-*•]|\d+\.)[ ]+/, '' ).trim() ).slice( 0, 5 )
        : [ "Debug analysis based on your screenshots" ];

      const response = {
        code: extractedCode,
        debug_analysis: formattedDebugContent,
        thoughts: thoughts,
        time_complexity: "N/A - Debug mode",
        space_complexity: "N/A - Debug mode"
      };

      return { success: true, data: response };
    } catch ( error: any )
    {
      console.error( "Debug processing error:", error );
      return { success: false, error: error.message || "Failed to process debug request" };
    }
  }

  public cancelOngoingRequests (): void
  {
    let wasCancelled = false

    if ( this.currentProcessingAbortController )
    {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if ( this.currentExtraProcessingAbortController )
    {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    this.deps.setHasDebugged( false )

    this.deps.setProblemInfo( null )

    const mainWindow = this.deps.getMainWindow()
    if ( wasCancelled && mainWindow && !mainWindow.isDestroyed() )
    {
      mainWindow.webContents.send( this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS )
    }
  }

  /**
   * Send chat message to AI
   */
  public async sendChatMessage ( data: {
    message: string;
    language: string;
    conversationHistory: Array<{ role: string; content: string }>;
  } ): Promise<{ success: boolean; content?: string; error?: string }>
  {
    try
    {
      const config = configHelper.loadConfig();

      // Prepare conversation context
      const systemMessage = `You are a helpful coding assistant. The user is working in ${ data.language }.
      Provide clear, concise answers. When showing code, use proper formatting with code blocks.
      Be encouraging and educational in your responses.`;

      const messages = [
        { role: "system", content: systemMessage },
        ...data.conversationHistory.slice( -8 ), // Keep last 8 messages for context
        { role: "user", content: data.message }
      ];

      if ( config.apiProvider === "openai" )
      {
        if ( !this.openaiClient )
        {
          return { success: false, error: "OpenAI API not configured" };
        }

        const response = await this.openaiClient.chat.completions.create( {
          model: config.solutionModel || "gpt-4o",
          messages: messages as any,
          max_tokens: 2000,
          temperature: 0.7
        } );

        const content = response.choices[ 0 ].message.content;
        return { success: true, content: content || "No response generated" };

      } else if ( config.apiProvider === "gemini" )
      {
        if ( !this.geminiApiKey )
        {
          return { success: false, error: "Gemini API not configured" };
        }

        // Convert to Gemini format
        const geminiMessages = messages.filter( m => m.role !== "system" ).map( m => ( {
          role: m.role === "assistant" ? "model" : "user",
          parts: [ { text: m.content } ]
        } ) );

        const response = await axios.default.post(
          `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${ this.geminiApiKey }`,
          {
            contents: geminiMessages,
            systemInstruction: { parts: [ { text: systemMessage } ] },
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 2000
            }
          }
        );

        const content = response.data.candidates?.[ 0 ]?.content?.parts?.[ 0 ]?.text;
        return { success: true, content: content || "No response generated" };

      } else if ( config.apiProvider === "anthropic" )
      {
        if ( !this.anthropicClient )
        {
          return { success: false, error: "Anthropic API not configured" };
        }

        const response = await this.anthropicClient.messages.create( {
          model: config.solutionModel || "claude-3-5-sonnet-20241022",
          max_tokens: 2000,
          temperature: 0.7,
          system: systemMessage,
          messages: messages.filter( m => m.role !== "system" ) as any
        } );

        const content = response.content[ 0 ].type === "text" ? response.content[ 0 ].text : "No response generated";
        return { success: true, content };

      } else if ( config.apiProvider === "qwen" )
      {
        if ( !this.qwenApiKey )
        {
          return { success: false, error: "Qwen API not configured" };
        }

        const response = await axios.default.post(
          "https://openrouter.ai/api/v1/chat/completions",
          {
            model: config.solutionModel || "qwen/qwq-32b:free",
            messages: messages,
            max_tokens: 2000,
            temperature: 0.7
          },
          {
            headers: {
              "Authorization": `Bearer ${ this.qwenApiKey }`,
              "Content-Type": "application/json"
            }
          }
        );

        const content = response.data.choices[ 0 ].message.content;
        return { success: true, content: content || "No response generated" };
      }

      return { success: false, error: "No API provider configured" };

    } catch ( error )
    {
      console.error( "Chat message error:", error );
      return { success: false, error: "Failed to get AI response" };
    }
  }

  /**
   * Process voice transcription with Groq AI
   */
  public async processVoiceTranscription ( transcription: string, interviewType: string ): Promise<{ success: boolean; content?: string; error?: string }>
  {
    try
    {
      if ( !this.groqApiKey )
      {
        return { success: false, error: "Groq API key not configured for voice processing" };
      }

      // Create specialized prompt based on interview type
      let systemPrompt = "";
      switch ( interviewType )
      {
        case "technical":
          systemPrompt = `You are an expert technical interviewer. The user has spoken a technical interview question or problem.
          Provide a detailed, step-by-step solution with code examples. Focus on algorithm efficiency, code quality, and best practices.
          Format your response with clear sections: Problem Understanding, Approach, Code Implementation, Complexity Analysis, and Edge Cases.`;
          break;
        case "behavioral":
          systemPrompt = `You are an experienced HR interviewer. The user has spoken a behavioral interview question.
          Provide thoughtful guidance using the STAR method (Situation, Task, Action, Result) and help structure compelling answers.
          Give specific examples and tips for delivering impactful responses.`;
          break;
        case "system-design":
          systemPrompt = `You are a senior system architect. The user has spoken a system design question.
          Help design scalable systems with clear explanations covering: Requirements, High-level Design, Database Design,
          API Design, Scalability considerations, and Trade-offs.`;
          break;
        default:
          systemPrompt = `You are a helpful interview coach. The user has spoken an interview question.
          Provide comprehensive guidance and practical advice. Be encouraging and thorough in your response.`;
          break;
      }

      const messages = [
        { role: "system", content: systemPrompt },
        { role: "user", content: `Voice transcription: "${ transcription }"\n\nPlease provide detailed guidance for this interview question.` }
      ];

      // Make API request to Groq
      const response = await axios.default.post(
        "https://api.groq.com/openai/v1/chat/completions",
        {
          model: "llama-3.1-70b-versatile", // Fast and capable model for voice processing
          messages: messages,
          max_tokens: 2000,
          temperature: 0.7
        },
        {
          headers: {
            "Authorization": `Bearer ${ this.groqApiKey }`,
            "Content-Type": "application/json"
          }
        }
      );

      const content = response.data.choices[ 0 ].message.content;
      return { success: true, content: content || "No response generated" };

    } catch ( error: any )
    {
      console.error( "Groq voice processing error:", error );

      if ( error?.response?.status === 401 )
      {
        return { success: false, error: "Invalid Groq API key. Please check your configuration." };
      } else if ( error?.response?.status === 429 )
      {
        return { success: false, error: "Groq API rate limit exceeded. Please try again later." };
      }

      return { success: false, error: "Failed to process voice with Groq AI" };
    }
  }
}
