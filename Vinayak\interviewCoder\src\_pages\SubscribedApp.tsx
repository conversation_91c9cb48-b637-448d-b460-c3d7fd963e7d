// file: src/components/SubscribedApp.tsx
import { useQueryClient } from "@tanstack/react-query"
import { useEffect, useRef, useState } from "react"
import Queue from "../_pages/Queue"
import Solutions from "../_pages/Solutions"
import ChatbotMode from "../_pages/ChatbotMode"
import InterviewMode from "../_pages/InterviewMode"
import { useToast } from "../contexts/toast"
import { LanguageIndicator } from "../components/shared/LanguageIndicator"

const LANGUAGE_DISPLAY_NAMES: Record<string, string> = {
  'c': 'C',
  'cpp': 'C++',
  'java': 'Java',
  'sql': 'SQL',
  'javascript': 'JavaScript',
  'python': 'Python',
  'csharp': 'C#',
  'go': 'Go',
  'rust': 'Rust',
  'typescript': 'TypeScript'
}

interface SubscribedAppProps
{
  credits: number
  currentLanguage: string
  setLanguage: ( language: string ) => void
}

const SubscribedApp: React.FC<SubscribedAppProps> = ( {
  credits,
  currentLanguage,
  setLanguage
} ) =>
{
  const queryClient = useQueryClient()
  const [ view, setView ] = useState<"queue" | "solutions" | "debug" | "chatbot" | "interview">( "queue" )
  const containerRef = useRef<HTMLDivElement>( null )
  const { showToast } = useToast()

  // Keyboard shortcuts for mode switching and electron IPC listeners
  useEffect( () =>
  {
    const handleKeyDown = ( event: KeyboardEvent ) =>
    {
      if ( event.ctrlKey || event.metaKey )
      {
        switch ( event.key )
        {
          case 'd':
            event.preventDefault()
            setView( "queue" ) // DSA mode
            showToast( "Mode Switch", "Switched to DSA mode", "neutral" )
            break
          case 'c':
            event.preventDefault()
            setView( "chatbot" ) // Chatbot mode
            showToast( "Mode Switch", "Switched to Chatbot mode", "neutral" )
            break
          case 'i':
            event.preventDefault()
            setView( "interview" ) // Interview mode
            showToast( "Mode Switch", "Switched to Interview mode", "neutral" )
            break
          case 'n':
            event.preventDefault()
            setView( "interview" ) // Interview mode (alternative)
            showToast( "Mode Switch", "Switched to Interview mode", "neutral" )
            break
        }
      }
    }

    // Listen for electron mode switching events
    const handleModeSwitch = ( _event: any, mode: string ) =>
    {
      console.log( `🎯 Electron mode switch: ${ mode }` )
      switch ( mode )
      {
        case "queue":
          setView( "queue" )
          showToast( "Mode Switch", "Switched to DSA mode", "neutral" )
          break
        case "chatbot":
          setView( "chatbot" )
          showToast( "Mode Switch", "Switched to Chatbot mode", "neutral" )
          break
        case "interview":
          setView( "interview" )
          showToast( "Mode Switch", "Switched to Interview mode", "neutral" )
          break
        default:
          console.warn( `Unknown mode: ${ mode }` )
      }
    }

    // Listen for language switching events
    const handleLanguageSwitch = ( _event: any, language: string ) =>
    {
      console.log( `🎯 Language switch: ${ language }` )
      setLanguage( language )
      showToast( "Language Changed", `Switched to ${ language.toUpperCase() }`, "neutral" )
    }

    document.addEventListener( 'keydown', handleKeyDown )

    // Add electron event listeners
    if ( window.electronAPI )
    {
      window.electronAPI.ipcRenderer?.on( 'switch-mode', handleModeSwitch )
      window.electronAPI.ipcRenderer?.on( 'set-language', handleLanguageSwitch )
    }

    return () =>
    {
      document.removeEventListener( 'keydown', handleKeyDown )
      // Remove electron listeners
      if ( window.electronAPI )
      {
        window.electronAPI.ipcRenderer?.removeListener( 'switch-mode', handleModeSwitch )
        window.electronAPI.ipcRenderer?.removeListener( 'set-language', handleLanguageSwitch )
      }
    }
  }, [] )

  // Let's ensure we reset queries etc. if some electron signals happen
  useEffect( () =>
  {
    const cleanup = window.electronAPI.onResetView( () =>
    {
      queryClient.invalidateQueries( {
        queryKey: [ "screenshots" ]
      } )
      queryClient.invalidateQueries( {
        queryKey: [ "problem_statement" ]
      } )
      queryClient.invalidateQueries( {
        queryKey: [ "solution" ]
      } )
      queryClient.invalidateQueries( {
        queryKey: [ "new_solution" ]
      } )
      setView( "queue" )
    } )

    return () =>
    {
      cleanup()
    }
  }, [] )

  // Dynamically update the window size
  useEffect( () =>
  {
    if ( !containerRef.current ) return

    const updateDimensions = () =>
    {
      if ( !containerRef.current ) return
      const height = containerRef.current.scrollHeight || 600
      const width = containerRef.current.scrollWidth || 800
      window.electronAPI?.updateContentDimensions( { width, height } )
    }

    // Force initial dimension update immediately
    updateDimensions()

    // Set a fallback timer to ensure dimensions are set even if content isn't fully loaded
    const fallbackTimer = setTimeout( () =>
    {
      window.electronAPI?.updateContentDimensions( { width: 800, height: 600 } )
    }, 500 )

    const resizeObserver = new ResizeObserver( updateDimensions )
    resizeObserver.observe( containerRef.current )

    // Also watch DOM changes
    const mutationObserver = new MutationObserver( updateDimensions )
    mutationObserver.observe( containerRef.current, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    } )

    // Do another update after a delay to catch any late-loading content
    const delayedUpdate = setTimeout( updateDimensions, 1000 )

    return () =>
    {
      resizeObserver.disconnect()
      mutationObserver.disconnect()
      clearTimeout( fallbackTimer )
      clearTimeout( delayedUpdate )
    }
  }, [ view ] )

  // Listen for language changes from keyboard shortcuts
  useEffect( () =>
  {
    const cleanup = window.electronAPI.onLanguageChange( ( language: string ) =>
    {
      console.log( "Language changed via shortcut:", language )
      setLanguage( language )
      showToast( "Language Changed", `Switched to ${ language.toUpperCase() }`, "neutral" )
    } )

    return cleanup
  }, [ setLanguage, showToast ] )

  // Listen for processing mode changes
  useEffect( () =>
  {
    const cleanup = window.electronAPI.onProcessingModeChange( ( mode: string ) =>
    {
      console.log( "Processing mode changed:", mode )
      showToast( "Processing Mode", `Switched to ${ mode.toUpperCase() } mode`, "neutral" )
    } )

    return cleanup
  }, [ showToast ] )

  // Listen for events that might switch views or show errors
  useEffect( () =>
  {
    const cleanupFunctions = [
      window.electronAPI.onSolutionStart( () =>
      {
        setView( "solutions" )
      } ),
      window.electronAPI.onUnauthorized( () =>
      {
        queryClient.removeQueries( {
          queryKey: [ "screenshots" ]
        } )
        queryClient.removeQueries( {
          queryKey: [ "solution" ]
        } )
        queryClient.removeQueries( {
          queryKey: [ "problem_statement" ]
        } )
        setView( "queue" )
      } ),
      window.electronAPI.onResetView( () =>
      {
        queryClient.removeQueries( {
          queryKey: [ "screenshots" ]
        } )
        queryClient.removeQueries( {
          queryKey: [ "solution" ]
        } )
        queryClient.removeQueries( {
          queryKey: [ "problem_statement" ]
        } )
        setView( "queue" )
      } ),
      window.electronAPI.onResetView( () =>
      {
        queryClient.setQueryData( [ "problem_statement" ], null )
      } ),
      window.electronAPI.onProblemExtracted( ( data: any ) =>
      {
        if ( view === "queue" )
        {
          queryClient.invalidateQueries( {
            queryKey: [ "problem_statement" ]
          } )
          queryClient.setQueryData( [ "problem_statement" ], data )
        }
      } ),
      window.electronAPI.onSolutionError( ( error: string ) =>
      {
        showToast( "Error", error, "error" )
      } )
    ]
    return () => cleanupFunctions.forEach( ( fn ) => fn() )
  }, [ view ] )

  return (
    <div ref={containerRef} className="min-h-0">
      {/* Language indicator overlay */}
      <LanguageIndicator currentLanguage={currentLanguage} />

      {/* Mode indicator */}
      <div className="px-4 py-1 bg-black/10 border-b border-white/5">
        <div className="flex items-center justify-between text-xs text-white/60">
          <div className="flex items-center gap-4">
            <span className={`px-2 py-1 rounded ${ view === "queue" || view === "solutions" ? "bg-blue-600/80 text-white" : "" }`}>
              🧮 DSA Mode {view === "queue" || view === "solutions" ? "(Active)" : ""}
            </span>
            <span className={`px-2 py-1 rounded ${ view === "chatbot" ? "bg-green-600/80 text-white" : "" }`}>
              💬 Chatbot Mode {view === "chatbot" ? "(Active)" : ""}
            </span>
            <span className={`px-2 py-1 rounded ${ view === "interview" ? "bg-purple-600/80 text-white" : "" }`}>
              🎤 Interview Mode {view === "interview" ? "(Active)" : ""}
            </span>
            <span className="px-2 py-1 rounded bg-orange-600/80 text-white">
              🔤 {LANGUAGE_DISPLAY_NAMES[ currentLanguage ] || currentLanguage.toUpperCase()}
            </span>
          </div>
          <span>Ctrl+D DSA • Ctrl+C Chatbot • Ctrl+I Interview • Alt+0-4 Language</span>
        </div>
      </div>

      {view === "queue" ? (
        <Queue
          setView={setView}
          credits={credits}
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : view === "solutions" ? (
        <Solutions
          setView={setView}
          credits={credits}
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : view === "chatbot" ? (
        <ChatbotMode
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : view === "interview" ? (
        <InterviewMode
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : null}
    </div>
  )
}

export default SubscribedApp
