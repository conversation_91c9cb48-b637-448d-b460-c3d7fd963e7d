import React, { useState, useEffect, useRef } from "react"
import { useQuery } from "@tanstack/react-query"
import ScreenshotQueue from "../components/Queue/ScreenshotQueue"
import QueueCommands from "../components/Queue/QueueCommands"
import MultipleScreenshotAnalyzer from "../components/DSA/MultipleScreenshotAnalyzer"

import { useToast } from "../contexts/toast"
import { Screenshot } from "../types/screenshots"

async function fetchScreenshots (): Promise<Screenshot[]>
{
  try
  {
    const existing = await window.electronAPI.getScreenshots()
    return existing
  } catch ( error )
  {
    console.error( "Error loading screenshots:", error )
    throw error
  }
}

interface QueueProps
{
  setView: ( view: "queue" | "solutions" | "debug" ) => void
  credits: number
  currentLanguage: string
  setLanguage: ( language: string ) => void
}

const Queue: React.FC<QueueProps> = ( {
  setView,
  credits,
  currentLanguage,
  setLanguage
} ) =>
{
  const { showToast } = useToast()

  const [ isTooltipVisible, setIsTooltipVisible ] = useState( false )
  const [ tooltipHeight, setTooltipHeight ] = useState( 0 )
  const [ isProcessing, setIsProcessing ] = useState( false )
  const [ showAdvancedAnalyzer, setShowAdvancedAnalyzer ] = useState( false )
  const contentRef = useRef<HTMLDivElement>( null )

  const {
    data: screenshots = [],
    isLoading,
    refetch
  } = useQuery<Screenshot[]>( {
    queryKey: [ "screenshots" ],
    queryFn: fetchScreenshots,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnWindowFocus: false
  } )

  const handleDeleteScreenshot = async ( index: number ) =>
  {
    const screenshotToDelete = screenshots[ index ]

    try
    {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if ( response.success )
      {
        refetch() // Refetch screenshots instead of managing state directly
      } else
      {
        console.error( "Failed to delete screenshot:", response.error )
        showToast( "Error", "Failed to delete the screenshot file", "error" )
      }
    } catch ( error )
    {
      console.error( "Error deleting screenshot:", error )
    }
  }

  const handleAnalyzeScreenshots = async (
    selectedScreenshots: Screenshot[],
    mode: "normal" | "fast" | "mcq"
  ) =>
  {
    setIsProcessing( true )

    try
    {
      // Set processing mode for the backend
      if ( mode !== "normal" )
      {
        showToast( "Processing Mode", `Switched to ${ mode.toUpperCase() } mode`, "neutral" )
      }

      // Trigger the processing
      await window.electronAPI.processScreenshots()

      showToast(
        "Processing Started",
        `Analyzing ${ selectedScreenshots.length } screenshot${ selectedScreenshots.length !== 1 ? 's' : '' } in ${ mode } mode`,
        "success"
      )
    } catch ( error )
    {
      console.error( "Error starting analysis:", error )
      showToast( "Error", "Failed to start screenshot analysis", "error" )
    } finally
    {
      setIsProcessing( false )
    }
  }

  const handleClearResponses = async () =>
  {
    try
    {
      // Clear the solutions view and reset to queue
      setView( "queue" )

      // Trigger reset in electron backend
      await window.electronAPI.resetView()

      showToast( "Responses Cleared", "All DSA responses have been cleared", "success" )
    } catch ( error )
    {
      console.error( "Error clearing responses:", error )
      showToast( "Error", "Failed to clear responses", "error" )
    }
  }

  // Language shortcuts and clear responses for DSA mode
  useEffect( () =>
  {
    const handleKeyDown = ( event: KeyboardEvent ) =>
    {
      if ( event.altKey )
      {
        switch ( event.key )
        {
          case '0':
            event.preventDefault()
            setLanguage( "c" )
            showToast( "Language Changed", "Switched to C", "neutral" )
            break
          case '1':
            event.preventDefault()
            setLanguage( "cpp" )
            showToast( "Language Changed", "Switched to C++", "neutral" )
            break
          case '2':
            event.preventDefault()
            setLanguage( "java" )
            showToast( "Language Changed", "Switched to Java", "neutral" )
            break
          case '3':
            event.preventDefault()
            setLanguage( "sql" )
            showToast( "Language Changed", "Switched to SQL", "neutral" )
            break
          case '4':
            event.preventDefault()
            setLanguage( "javascript" )
            showToast( "Language Changed", "Switched to JavaScript", "neutral" )
            break
          case 'l':
          case 'L':
            event.preventDefault()
            handleClearResponses()
            break
        }
      }
    }

    document.addEventListener( 'keydown', handleKeyDown )
    return () =>
    {
      document.removeEventListener( 'keydown', handleKeyDown )
    }
  }, [ setLanguage, showToast ] )

  useEffect( () =>
  {
    // Height update logic
    const updateDimensions = () =>
    {
      if ( contentRef.current )
      {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if ( isTooltipVisible )
        {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions( {
          width: contentWidth,
          height: contentHeight
        } )
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver( updateDimensions )
    if ( contentRef.current )
    {
      resizeObserver.observe( contentRef.current )
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken( () => refetch() ),
      window.electronAPI.onResetView( () => refetch() ),
      window.electronAPI.onDeleteLastScreenshot( async () =>
      {
        if ( screenshots.length > 0 )
        {
          const lastScreenshot = screenshots[ screenshots.length - 1 ];
          await handleDeleteScreenshot( screenshots.length - 1 );
          // Toast removed as requested
        } else
        {
          showToast( "No Screenshots", "There are no screenshots to delete", "neutral" );
        }
      } ),
      window.electronAPI.onSolutionError( ( error: string ) =>
      {
        showToast(
          "Processing Failed",
          "There was an error processing your screenshots.",
          "error"
        )
        setView( "queue" ) // Revert to queue if processing fails
        console.error( "Processing error:", error )
      } ),
      window.electronAPI.onProcessingNoScreenshots( () =>
      {
        showToast(
          "No Screenshots",
          "There are no screenshots to process.",
          "neutral"
        )
      } ),
      // Removed out of credits handler - unlimited credits in this version
    ]

    return () =>
    {
      resizeObserver.disconnect()
      cleanupFunctions.forEach( ( cleanup ) => cleanup() )
    }
  }, [ isTooltipVisible, tooltipHeight, screenshots ] )

  const handleTooltipVisibilityChange = ( visible: boolean, height: number ) =>
  {
    setIsTooltipVisible( visible )
    setTooltipHeight( height )
  }

  const handleOpenSettings = () =>
  {
    window.electronAPI.openSettingsPortal();
  };

  return (
    <div ref={contentRef} className={`bg-transparent w-1/2`}>
      <div className="px-4 py-3">
        <div className="space-y-3 w-fit">
          <ScreenshotQueue
            isLoading={false}
            screenshots={screenshots}
            onDeleteScreenshot={handleDeleteScreenshot}
          />

          {/* Toggle for Advanced Analyzer */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowAdvancedAnalyzer( !showAdvancedAnalyzer )}
              className="text-xs text-blue-400 hover:text-blue-300 underline"
            >
              {showAdvancedAnalyzer ? "Hide" : "Show"} Advanced Multi-Screenshot Analyzer
            </button>
            <span className="text-xs text-white/60">
              {screenshots.length} screenshot{screenshots.length !== 1 ? 's' : ''} available
            </span>
          </div>

          {/* Advanced Multiple Screenshot Analyzer */}
          {showAdvancedAnalyzer && (
            <div className="bg-black/20 border border-white/10 rounded-lg p-4">
              <MultipleScreenshotAnalyzer
                screenshots={screenshots}
                onAnalyze={handleAnalyzeScreenshots}
                isProcessing={isProcessing}
                currentLanguage={currentLanguage}
              />
            </div>
          )}

          <QueueCommands
            onTooltipVisibilityChange={handleTooltipVisibilityChange}
            screenshotCount={screenshots.length}
            credits={credits}
            currentLanguage={currentLanguage}
            setLanguage={setLanguage}
          />
        </div>
      </div>
    </div>
  )
}

export default Queue
