@echo off
echo.
echo ========================================
echo 🚀 INTERVIEW CODER - READY FOR ACTION!
echo ========================================
echo.
echo 🎯 Starting your stealth coding assistant...
echo 📋 All features implemented and ready!
echo.
echo ⌨️  KEYBOARD SHORTCUTS:
echo ----------------------
echo Ctrl+B  → Toggle window visibility
echo Ctrl+D  → DSA mode (coding problems)
echo Ctrl+C  → Chatbot mode (AI help)
echo Ctrl+N  → Interview mode (voice features)
echo Ctrl+H  → Take screenshot
echo Ctrl+Enter → Analyze with AI
echo Ctrl+Shift+C → Copy code
echo Ctrl+M  → Stealth window sizing
echo Ctrl+Q  → Quit application
echo.
echo 🎤 VOICE FEATURES:
echo ------------------
echo Start Listen → Capture interviewer voice
echo Stop Listen  → Get AI guidance
echo.
echo 🥷 STEALTH FEATURES:
echo -------------------
echo Progressive sizing (5 levels)
echo Opacity controls (Ctrl+[/])
echo Invisible startup
echo.
echo 🚀 Starting application...
echo.

cd /d "%~dp0"

REM Check if dist folder exists, if not build the app
if not exist "dist" (
    echo Building production version...
    npm run build
    echo.
)

REM Start in production mode (stealth mode - no dev tools)
npm run run-prod

pause
