import React, { useState, useEffect, useRef } from "react"
import { useToast } from "../contexts/toast"

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: number
  codeBlocks?: string[]
}

interface ChatbotModeProps {
  currentLanguage: string
  setLanguage: (language: string) => void
}

const ChatbotMode: React.FC<ChatbotModeProps> = ({
  currentLanguage,
  setLanguage
}) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [inputFocused, setInputFocused] = useState(false)
  
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const messagesRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  
  const { showToast } = useToast()

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (messagesRef.current) {
      messagesRef.current.scrollTop = messagesRef.current.scrollHeight
    }
  }, [messages])

  // Update window dimensions
  useEffect(() => {
    const updateDimensions = () => {
      if (contentRef.current) {
        const contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
    }
  }, [messages])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'i':
            event.preventDefault()
            setInputFocused(true)
            if (inputRef.current) {
              inputRef.current.focus()
            }
            break
          case 'Enter':
            event.preventDefault()
            if (inputValue.trim() && !isLoading) {
              handleSendMessage()
            }
            break
          case 'l':
            event.preventDefault()
            handleClearChat()
            break
        }
      }
      
      // Regular Enter to send message when input is focused
      if (event.key === 'Enter' && !event.shiftKey && inputFocused && inputValue.trim() && !isLoading) {
        event.preventDefault()
        handleSendMessage()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [inputValue, isLoading, inputFocused])

  const extractCodeBlocks = (content: string): string[] => {
    const codeBlockRegex = /```[\s\S]*?```/g
    const matches = content.match(codeBlockRegex) || []
    return matches.map(match => match.replace(/```\w*\n?|```/g, '').trim())
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputValue.trim(),
      timestamp: Date.now()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")
    setIsLoading(true)

    try {
      // Call the AI API through electron
      const response = await window.electronAPI.sendChatMessage({
        message: inputValue.trim(),
        language: currentLanguage,
        conversationHistory: messages.slice(-10) // Send last 10 messages for context
      })

      if (response.success) {
        const codeBlocks = extractCodeBlocks(response.content)
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: "assistant",
          content: response.content,
          timestamp: Date.now(),
          codeBlocks
        }
        setMessages(prev => [...prev, assistantMessage])
      } else {
        showToast("Error", response.error || "Failed to get response", "error")
      }
    } catch (error) {
      console.error("Chat error:", error)
      showToast("Error", "Failed to send message", "error")
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearChat = () => {
    setMessages([])
    showToast("Chat Cleared", "Conversation history has been cleared", "neutral")
  }

  const copyCodeBlock = (code: string, index: number) => {
    navigator.clipboard.writeText(code).then(() => {
      showToast("Copied", `Code block ${index + 1} copied to clipboard`, "success")
    })
  }

  const copyLastCodeBlock = () => {
    const lastMessage = messages.filter(m => m.role === "assistant").pop()
    if (lastMessage?.codeBlocks && lastMessage.codeBlocks.length > 0) {
      const lastCode = lastMessage.codeBlocks[lastMessage.codeBlocks.length - 1]
      navigator.clipboard.writeText(lastCode).then(() => {
        showToast("Copied", "Last code block copied to clipboard", "success")
      })
    }
  }

  return (
    <div ref={contentRef} className="flex flex-col h-full bg-transparent">
      {/* Header */}
      <div className="px-4 py-2 bg-black/20 border-b border-white/10">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-white">💬 Chatbot Mode</h2>
          <div className="flex items-center gap-2 text-xs text-white/60">
            <span>Ctrl+I to focus • Ctrl+Enter to send • Ctrl+L to clear</span>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div 
        ref={messagesRef}
        className="flex-1 overflow-y-auto px-4 py-3 space-y-3 max-h-80"
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.1)'
        }}
      >
        {messages.length === 0 ? (
          <div className="text-center text-white/60 py-8">
            <p className="text-sm">Start a conversation with the AI assistant</p>
            <p className="text-xs mt-2">Ask coding questions, get explanations, or request help with algorithms</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
              <div className={`max-w-[80%] rounded-lg px-3 py-2 ${
                message.role === "user" 
                  ? "bg-blue-600/80 text-white" 
                  : "bg-white/10 text-gray-100"
              }`}>
                <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                {message.codeBlocks && message.codeBlocks.length > 0 && (
                  <div className="mt-2 space-y-1">
                    {message.codeBlocks.map((code, index) => (
                      <div key={index} className="relative">
                        <button
                          onClick={() => copyCodeBlock(code, index)}
                          className="absolute top-1 right-1 text-xs bg-black/50 hover:bg-black/70 rounded px-1 py-0.5 text-white/80"
                        >
                          Copy
                        </button>
                        <pre className="bg-black/30 rounded p-2 text-xs overflow-x-auto">
                          <code>{code}</code>
                        </pre>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-white/10 text-gray-100 rounded-lg px-3 py-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                <span className="text-xs text-white/60 ml-2">AI is thinking...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="px-4 py-3 bg-black/20 border-t border-white/10">
        <div className="flex gap-2">
          <textarea
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onFocus={() => setInputFocused(true)}
            onBlur={() => setInputFocused(false)}
            placeholder="Ask me anything about coding..."
            className={`flex-1 bg-black/50 border rounded-lg px-3 py-2 text-white text-sm resize-none ${
              inputFocused ? 'border-blue-400/50 ring-1 ring-blue-400/30' : 'border-white/10'
            }`}
            rows={2}
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-4 py-2 bg-blue-600/80 hover:bg-blue-600 disabled:bg-white/10 disabled:text-white/40 text-white rounded-lg text-sm font-medium transition-colors"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  )
}

export default ChatbotMode
